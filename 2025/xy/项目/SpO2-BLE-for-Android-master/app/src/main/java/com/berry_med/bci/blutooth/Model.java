package com.berry_med.bci.blutooth;

/*
 * description: <PERSON><PERSON> Model
 * author: zl
 * date: 2024/10/23 9:47
 */
public class Model {
    public static final String UUID_SERVICE_DATA           = "49535343-fe7d-4ae5-8fa9-9fafd205e455";
    public static final String CHARACTERISTIC_UUID_SEND    = "49535343-1e4d-4bd9-ba61-23c647249616";
    public static final String CHARACTERISTIC_UUID_RECEIVE = "*************-43f4-a8d4-ecbe34729bb3";
    public static final String CHARACTERISTIC_UUID_RENAME  = "00005343-0000-1000-8000-00805f9b34fb"; //rename device

    public static String MODEL = "BCI";
}
